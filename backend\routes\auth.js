const express = require('express');
const bcrypt = require('bcrypt');
const crypto = require('crypto');
const User = require('../models/User');
const sendEmail = require('../utils/mailer');

const router = express.Router();

// Rate limiting helper function
const checkRateLimit = async (email, ip) => {
  const user = await User.findOne({ email });
  if (!user) {
    // Don't reveal if email exists, but still apply rate limiting
    // Check for attempts from this IP in the last hour
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
    const recentAttempts = await User.aggregate([
      { $unwind: '$passwordResetAttempts' },
      {
        $match: {
          'passwordResetAttempts.ip': ip,
          'passwordResetAttempts.timestamp': { $gte: oneHourAgo }
        }
      },
      { $count: 'total' }
    ]);
    
    const attemptCount = recentAttempts.length > 0 ? recentAttempts[0].total : 0;
    return attemptCount < 3;
  }

  // Clean up old attempts (older than 1 hour)
  const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
  user.passwordResetAttempts = user.passwordResetAttempts.filter(
    attempt => attempt.timestamp > oneHourAgo
  );

  // Check if user has exceeded rate limit
  const recentAttempts = user.passwordResetAttempts.length;
  return recentAttempts < 3;
};

// Record attempt
const recordAttempt = async (email, ip) => {
  const user = await User.findOne({ email });
  if (user) {
    user.passwordResetAttempts.push({ timestamp: new Date(), ip });
    await user.save();
  } else {
    // For non-existent emails, we still need to track IP-based attempts
    // Find any user to store the attempt (for IP tracking)
    const anyUser = await User.findOne({});
    if (anyUser) {
      anyUser.passwordResetAttempts.push({ timestamp: new Date(), ip });
      await anyUser.save();
    }
  }
};

// POST /api/auth/forgot-password
router.post('/forgot-password', async (req, res) => {
  try {
    const { email } = req.body;
    const ip = req.ip || req.connection.remoteAddress || 'unknown';

    // Input validation
    if (!email || !email.trim()) {
      return res.status(400).json({ error: 'Email is required' });
    }

    // Email format validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email.trim())) {
      return res.status(400).json({ error: 'Please enter a valid email address' });
    }

    // Check rate limiting
    const canProceed = await checkRateLimit(email.trim().toLowerCase(), ip);
    if (!canProceed) {
      await recordAttempt(email.trim().toLowerCase(), ip);
      return res.status(429).json({ 
        error: 'Too many password reset attempts. Please try again later.' 
      });
    }

    // Record this attempt
    await recordAttempt(email.trim().toLowerCase(), ip);

    // Find user (but don't reveal if they exist)
    const user = await User.findOne({ 
      $or: [
        { email: email.trim().toLowerCase() },
        { username: email.trim().toLowerCase() }
      ]
    });

    // Always return success message (security best practice)
    const successMessage = 'If an account with that email exists, we have sent a password reset link.';

    if (!user) {
      // Don't reveal that user doesn't exist
      return res.status(200).json({ message: successMessage });
    }

    // Generate secure reset token
    const resetToken = crypto.randomBytes(32).toString('hex');
    const resetTokenExpiry = new Date(Date.now() + 15 * 60 * 1000); // 15 minutes

    // Save reset token to user
    user.passwordResetToken = resetToken;
    user.passwordResetExpires = resetTokenExpiry;
    await user.save();

    // Send reset email with token (mobile app approach)
    const emailHtml = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #0FAC86;">Password Reset Request</h2>
        <p>Hello ${user.name},</p>
        <p>You have requested to reset your password for your ADR (Adverse Drug Reaction) mobile app account.</p>

        <div style="background-color: #f5f5f5; padding: 20px; border-radius: 8px; margin: 20px 0; text-align: center;">
          <h3 style="color: #333; margin-top: 0;">Your Password Reset Token:</h3>
          <div style="background-color: #fff; padding: 15px; border-radius: 5px; border: 2px dashed #0FAC86; margin: 10px 0;">
            <span style="font-family: 'Courier New', monospace; font-size: 18px; font-weight: bold; color: #0FAC86; letter-spacing: 2px;">${resetToken}</span>
          </div>
          <p style="color: #666; font-size: 14px; margin-bottom: 0;">Copy this token and paste it in the mobile app</p>
        </div>

        <div style="background-color: #e8f5e8; padding: 15px; border-radius: 5px; border-left: 4px solid #4caf50;">
          <h4 style="color: #2e7d32; margin-top: 0;">How to reset your password:</h4>
          <ol style="color: #388e3c; margin-bottom: 0;">
            <li>Open the ADR mobile app</li>
            <li>Tap "Forgot Password?" on the login screen</li>
            <li>Tap "Enter Reset Token" button</li>
            <li>Paste the token shown above</li>
            <li>Create your new password</li>
          </ol>
        </div>

        <p><strong>Important Security Information:</strong></p>
        <ul>
          <li><strong>This token will expire in 15 minutes</strong></li>
          <li>If you didn't request this reset, please ignore this email</li>
          <li>Your password will not be changed until you create a new one in the app</li>
          <li>Do not share this token with anyone</li>
          <li>Only use this token in the official ADR mobile app</li>
        </ul>

        <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
        <p style="color: #666; font-size: 12px;">
          This email was sent from the ADR Reporting System mobile app. If you have any questions, please contact your system administrator.
        </p>
      </div>
    `;

    await sendEmail({
      to: user.email,
      subject: 'Password Reset Token - ADR Mobile App',
      text: `Hello ${user.name},

You have requested to reset your password for your ADR (Adverse Drug Reaction) mobile app account.

Your Password Reset Token: ${resetToken}

How to reset your password:
1. Open the ADR mobile app
2. Tap "Forgot Password?" on the login screen
3. Tap "Enter Reset Token" button
4. Paste the token: ${resetToken}
5. Create your new password

IMPORTANT:
- This token will expire in 15 minutes
- If you didn't request this reset, please ignore this email
- Do not share this token with anyone
- Only use this token in the official ADR mobile app

This email was sent from the ADR Reporting System mobile app.`,
      html: emailHtml
    });

    console.log(`Password reset email sent to ${user.email}`);
    res.status(200).json({ message: successMessage });

  } catch (error) {
    console.error('Forgot password error:', error);
    res.status(500).json({ error: 'An error occurred while processing your request' });
  }
});

// GET /api/auth/verify-reset-token
router.get('/verify-reset-token', async (req, res) => {
  try {
    const { token } = req.query;

    if (!token) {
      return res.status(400).json({ error: 'Reset token is required' });
    }

    const user = await User.findOne({
      passwordResetToken: token,
      passwordResetExpires: { $gt: new Date() }
    });

    if (!user) {
      return res.status(400).json({ error: 'Invalid or expired reset token' });
    }

    res.status(200).json({ 
      message: 'Token is valid',
      email: user.email // Return email for display purposes
    });

  } catch (error) {
    console.error('Verify token error:', error);
    res.status(500).json({ error: 'An error occurred while verifying the token' });
  }
});

// POST /api/auth/reset-password
router.post('/reset-password', async (req, res) => {
  try {
    const { token, newPassword, confirmPassword } = req.body;

    // Input validation
    if (!token || !newPassword || !confirmPassword) {
      return res.status(400).json({ error: 'All fields are required' });
    }

    if (newPassword !== confirmPassword) {
      return res.status(400).json({ error: 'Passwords do not match' });
    }

    // Password strength validation
    if (newPassword.length < 6) {
      return res.status(400).json({ error: 'Password must be at least 6 characters long' });
    }

    // Find user with valid reset token
    const user = await User.findOne({
      passwordResetToken: token,
      passwordResetExpires: { $gt: new Date() }
    });

    if (!user) {
      return res.status(400).json({ error: 'Invalid or expired reset token' });
    }

    // Hash new password
    const hashedPassword = await bcrypt.hash(newPassword, 10);

    // Update user password and clear reset token
    user.password = hashedPassword;
    user.passwordResetToken = null;
    user.passwordResetExpires = null;
    user.passwordResetAttempts = []; // Clear attempts after successful reset
    await user.save();

    // Send confirmation email
    const confirmationHtml = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #0FAC86;">Password Reset Successful</h2>
        <p>Hello ${user.name},</p>
        <p>Your password has been successfully reset for your ADR (Adverse Drug Reaction) account.</p>
        <p>You can now log in with your new password.</p>
        <p><strong>Security Notice:</strong></p>
        <ul>
          <li>If you did not make this change, please contact your system administrator immediately</li>
          <li>For your security, please ensure you're using a strong, unique password</li>
        </ul>
        <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
        <p style="color: #666; font-size: 12px;">
          This email was sent from the ADR Reporting System.
        </p>
      </div>
    `;

    await sendEmail({
      to: user.email,
      subject: 'Password Reset Successful - ADR System',
      text: `Hello ${user.name},\n\nYour password has been successfully reset. You can now log in with your new password.\n\nIf you did not make this change, please contact your system administrator immediately.`,
      html: confirmationHtml
    });

    console.log(`Password reset successful for user: ${user.email}`);
    res.status(200).json({ message: 'Password reset successful. You can now log in with your new password.' });

  } catch (error) {
    console.error('Reset password error:', error);
    res.status(500).json({ error: 'An error occurred while resetting your password' });
  }
});

module.exports = router;
