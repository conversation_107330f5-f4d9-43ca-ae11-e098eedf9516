import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView, Image } from 'react-native';
import { TextInput, Button, Text, ActivityIndicator } from 'react-native-paper';
import { theme, styles as globalStyles } from '../../theme';
import { useAuth } from '../../context/AuthContext';
import type { ResetPasswordData } from '../../types';

export const ResetPasswordScreen = ({ navigation, route }: any) => {
  const { resetPassword, verifyResetToken, isLoading, error } = useAuth();
  const [formData, setFormData] = useState<ResetPasswordData>({
    token: '',
    newPassword: '',
    confirmPassword: '',
  });
  const [formError, setFormError] = useState<string>('');
  const [successMessage, setSuccessMessage] = useState<string>('');
  const [userEmail, setUserEmail] = useState<string>('');
  const [passwordVisible, setPasswordVisible] = useState(false);
  const [confirmPasswordVisible, setConfirmPasswordVisible] = useState(false);
  const [tokenVerified, setTokenVerified] = useState(false);
  const [isDeepLink, setIsDeepLink] = useState(false);

  // Get token from route params or URL (if coming from email deep link)
  useEffect(() => {
    const token = route?.params?.token || '';
    if (token) {
      setIsDeepLink(true);
      setFormData(prev => ({ ...prev, token }));
      verifyToken(token);
    }
  }, [route?.params?.token]);

  const verifyToken = async (token: string) => {
    try {
      setFormError('');
      const result = await verifyResetToken(token);
      console.log('Setting userEmail to:', result.email);
      console.log('Setting tokenVerified to: true');
      setUserEmail(result.email);
      setTokenVerified(true);
    } catch (err) {
      let errorMessage = 'Invalid or expired reset token';

      if (err instanceof Error) {
        errorMessage = err.message;
      }

      // Add more specific error messages
      if (errorMessage.includes('Network Error') || errorMessage.includes('ECONNREFUSED')) {
        errorMessage = 'Cannot connect to server. Please check your internet connection.';
      } else if (errorMessage.includes('timeout')) {
        errorMessage = 'Request timed out. Please try again.';
      }

      console.log('Token verification failed, setting error:', errorMessage);
      setFormError(errorMessage);
      setTokenVerified(false);
    }
  };

  const handleTokenVerification = async () => {
    if (!formData.token.trim()) {
      setFormError('Please enter the reset token');
      return;
    }
    await verifyToken(formData.token.trim());
  };

  const validatePassword = (password: string): string | null => {
    if (password.length < 6) {
      return 'Password must be at least 6 characters long';
    }
    if (!/(?=.*[a-z])/.test(password)) {
      return 'Password must contain at least one lowercase letter';
    }
    if (!/(?=.*[A-Z])/.test(password)) {
      return 'Password must contain at least one uppercase letter';
    }
    if (!/(?=.*\d)/.test(password)) {
      return 'Password must contain at least one number';
    }
    return null;
  };

  const handleResetPassword = async () => {
    try {
      setFormError('');
      setSuccessMessage('');

      // Validation
      if (!formData.newPassword || !formData.confirmPassword) {
        throw new Error('Please fill in all fields');
      }

      if (formData.newPassword !== formData.confirmPassword) {
        throw new Error('Passwords do not match');
      }

      // Password strength validation
      const passwordError = validatePassword(formData.newPassword);
      if (passwordError) {
        throw new Error(passwordError);
      }

      const message = await resetPassword(formData);
      setSuccessMessage(message);
      
      // Clear form
      setFormData({ token: '', newPassword: '', confirmPassword: '' });

      // Navigate to login after 3 seconds
      setTimeout(() => {
        navigation.navigate('Login');
      }, 3000);

    } catch (err) {
      setFormError(err instanceof Error ? err.message : 'Failed to reset password');
    }
  };

  const navigateToLogin = () => {
    navigation.navigate('Login');
  };

  const navigateToForgotPassword = () => {
    navigation.navigate('ForgotPassword');
  };

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
        <Text style={styles.loadingText}>
          {tokenVerified ? 'Resetting password...' : 'Verifying token...'}
        </Text>
      </View>
    );
  }

  return (
    <ScrollView
      contentContainerStyle={styles.scrollContainer}
      keyboardShouldPersistTaps="handled"
    >
      <View style={styles.container}>
        <Image source={require('../../../assets/favicon.png')} style={styles.logo} />
        
        <View style={styles.header}>
          <Text style={styles.title}>Reset Password</Text>
          {userEmail ? (
            <Text style={styles.subtitle}>
              Reset password for: {userEmail}
            </Text>
          ) : (
            <Text style={styles.subtitle}>
              Paste the reset token from your email below
            </Text>
          )}
        </View>

        {(formError || error) && (
          <View style={styles.errorContainer}>
            <Text style={styles.error}>{formError || error}</Text>
          </View>
        )}

        {successMessage && (
          <View style={styles.successContainer}>
            <Text style={styles.success}>{successMessage}</Text>
            <Text style={styles.successNote}>
              Redirecting to login page...
            </Text>
          </View>
        )}

        {/* Debug info */}
        <View style={{ backgroundColor: '#f0f0f0', padding: 10, margin: 10 }}>
          <Text>Debug: tokenVerified = {tokenVerified.toString()}</Text>
          <Text>Debug: successMessage = {successMessage || 'null'}</Text>
          <Text>Debug: userEmail = {userEmail || 'null'}</Text>
          <Text>Debug: formError = {formError || 'null'}</Text>
        </View>

        {!tokenVerified && !successMessage && (
          <>
            <View style={styles.instructionContainer}>
              <Text style={styles.instructionTitle}>
                {isDeepLink ? '🔄 Verifying Reset Token...' : '📧 Enter Your Reset Token'}
              </Text>
              <Text style={styles.instructionText}>
                {isDeepLink
                  ? 'Please wait while we verify your reset token from the email link.'
                  : 'Copy the reset token from your email and paste it in the field below.'
                }
              </Text>
            </View>

            <TextInput
              label="Reset Token"
              value={formData.token}
              onChangeText={(text) => setFormData({ ...formData, token: text })}
              style={styles.input}
              mode="outlined"
              activeOutlineColor={theme.colors.primary}
              left={<TextInput.Icon icon="key" color={theme.colors.primary} />}
              placeholder="Paste your reset token here"
              autoCapitalize="none"
              autoCorrect={false}
              disabled={isDeepLink && !formError}
              multiline={true}
              numberOfLines={3}
            />



            <Button
              mode="contained"
              onPress={handleTokenVerification}
              style={styles.button}
              buttonColor={theme.colors.primary}
              disabled={isDeepLink && !formError}
            >
              {isDeepLink ? 'Verifying...' : 'Verify Token'}
            </Button>

            {isDeepLink && formError && (
              <Button
                mode="outlined"
                onPress={() => {
                  setIsDeepLink(false);
                  setFormError('');
                  setFormData({ ...formData, token: '' });
                }}
                style={styles.linkButton}
                textColor={theme.colors.primary}
              >
                Enter Token Manually
              </Button>
            )}
          </>
        )}

        {tokenVerified && !successMessage && (
          <>
            <TextInput
              label="New Password"
              value={formData.newPassword}
              onChangeText={(text) => setFormData({ ...formData, newPassword: text })}
              style={styles.input}
              mode="outlined"
              secureTextEntry={!passwordVisible}
              activeOutlineColor={theme.colors.primary}
              left={<TextInput.Icon icon="lock" color={theme.colors.primary} />}
              right={
                <TextInput.Icon
                  icon={passwordVisible ? "eye" : "eye-off"}
                  color={theme.colors.primary}
                  onPress={() => setPasswordVisible(!passwordVisible)}
                />
              }
            />

            <TextInput
              label="Confirm New Password"
              value={formData.confirmPassword}
              onChangeText={(text) => setFormData({ ...formData, confirmPassword: text })}
              style={styles.input}
              mode="outlined"
              secureTextEntry={!confirmPasswordVisible}
              activeOutlineColor={theme.colors.primary}
              left={<TextInput.Icon icon="lock-check" color={theme.colors.primary} />}
              right={
                <TextInput.Icon
                  icon={confirmPasswordVisible ? "eye" : "eye-off"}
                  color={theme.colors.primary}
                  onPress={() => setConfirmPasswordVisible(!confirmPasswordVisible)}
                />
              }
            />

            <View style={styles.passwordRequirements}>
              <Text style={styles.requirementsTitle}>Password Requirements:</Text>
              <Text style={styles.requirement}>• At least 6 characters long</Text>
              <Text style={styles.requirement}>• At least one lowercase letter</Text>
              <Text style={styles.requirement}>• At least one uppercase letter</Text>
              <Text style={styles.requirement}>• At least one number</Text>
            </View>

            <Button
              mode="contained"
              onPress={handleResetPassword}
              style={styles.button}
              buttonColor={theme.colors.primary}
            >
              Reset Password
            </Button>
          </>
        )}

        <Button
          mode="text"
          onPress={navigateToLogin}
          style={styles.linkButton}
          textColor={theme.colors.primary}
        >
          Back to Login
        </Button>

        {!tokenVerified && (
          <Button
            mode="text"
            onPress={navigateToForgotPassword}
            style={styles.linkButton}
            textColor={theme.colors.primary}
          >
            Request New Reset Link
          </Button>
        )}
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  scrollContainer: {
    flexGrow: 1,
    justifyContent: 'center',
  },
  container: {
    ...globalStyles.container,
    justifyContent: 'center',
    paddingHorizontal: theme.spacing.lg,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.background,
  },
  loadingText: {
    marginTop: theme.spacing.md,
    fontSize: 16,
    color: theme.colors.text,
  },
  logo: {
    width: 100,
    height: 100,
    alignSelf: 'center',
    marginBottom: theme.spacing.lg,
  },
  header: {
    alignItems: 'center',
    marginBottom: theme.spacing.xl,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: theme.colors.primary,
    marginBottom: theme.spacing.sm,
  },
  subtitle: {
    fontSize: 16,
    color: theme.colors.text,
    textAlign: 'center',
    lineHeight: 22,
    paddingHorizontal: theme.spacing.md,
  },
  input: {
    ...globalStyles.input,
    backgroundColor: theme.colors.surface,
    marginBottom: theme.spacing.md,
  },
  button: {
    ...globalStyles.button,
    marginVertical: theme.spacing.md,
  },
  linkButton: {
    marginVertical: theme.spacing.sm,
  },
  errorContainer: {
    backgroundColor: '#ffebee',
    padding: theme.spacing.md,
    borderRadius: 8,
    marginBottom: theme.spacing.md,
    borderLeftWidth: 4,
    borderLeftColor: '#f44336',
  },
  error: {
    color: '#d32f2f',
    fontSize: 14,
    textAlign: 'center',
  },
  successContainer: {
    backgroundColor: '#e8f5e8',
    padding: theme.spacing.md,
    borderRadius: 8,
    marginBottom: theme.spacing.md,
    borderLeftWidth: 4,
    borderLeftColor: '#4caf50',
  },
  success: {
    color: '#2e7d32',
    fontSize: 14,
    fontWeight: '600',
    textAlign: 'center',
    marginBottom: theme.spacing.sm,
  },
  successNote: {
    color: '#388e3c',
    fontSize: 12,
    textAlign: 'center',
    lineHeight: 16,
  },
  passwordRequirements: {
    backgroundColor: '#f5f5f5',
    padding: theme.spacing.md,
    borderRadius: 8,
    marginBottom: theme.spacing.md,
  },
  requirementsTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: theme.spacing.sm,
  },
  requirement: {
    fontSize: 12,
    color: theme.colors.text,
    marginBottom: 2,
  },
  instructionContainer: {
    backgroundColor: '#e3f2fd',
    padding: theme.spacing.md,
    borderRadius: 8,
    marginBottom: theme.spacing.md,
    borderLeftWidth: 4,
    borderLeftColor: '#2196f3',
  },
  instructionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1976d2',
    marginBottom: theme.spacing.sm,
  },
  instructionText: {
    fontSize: 14,
    color: '#1565c0',
    lineHeight: 20,
  },
});
