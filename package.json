{"name": "drugcounsellingapp", "version": "1.0.0", "main": "index.ts", "private": true, "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "build": "expo build:web"}, "dependencies": {"expo": "^53.0.9", "@expo/metro-runtime": "~5.0.4", "expo-constants": "~17.1.6", "expo-document-picker": "~13.1.5", "expo-image-picker": "~16.1.4", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.2", "react-native-web": "^0.20.0", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.10.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/datetimepicker": "8.3.0", "@react-native-picker/picker": "2.11.0", "@react-navigation/native": "^7.0.14", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native-stack": "^7.2.0", "axios": "^1.7.9", "exceljs": "^4.4.0", "@types/axios": "^0.14.4", "react-icons": "^5.5.0", "react-native-datepicker": "^1.7.2", "react-native-dotenv": "^3.4.11", "react-native-element-dropdown": "^2.12.4", "react-native-fs": "^2.20.0", "react-native-image-picker": "^8.0.0", "react-native-image-resizer": "^1.4.5", "react-native-paper": "^5.13.1", "react-native-paper-dates": "^0.22.40", "react-native-paper-dropdown": "^2.3.1", "react-native-photo-upload": "^1.3.0", "react-native-picker-select": "^9.3.1", "recharts": "^2.15.3", "react-native-vector-icons": "^10.2.0", "react-native-webview": "13.13.5", "socket.io-client": "^4.8.1"}, "devDependencies": {"@babel/core": "^7.26.0", "@types/react": "~19.0.10", "typescript": "~5.8.3"}}