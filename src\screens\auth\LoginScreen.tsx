import React, { useState } from 'react';
import { View, StyleSheet, ScrollView, Image } from 'react-native';
import { TextInput, Button, Text, ActivityIndicator } from 'react-native-paper';
import { theme, styles as globalStyles } from '../../theme';
import { useAuth } from '../../context/AuthContext';

export const LoginScreen = ({ navigation }: any) => {
  const { login, isLoading, error } = useAuth();
  const [formData, setFormData] = useState({
    email: '',
    password: '',
  });
  const [formError, setFormError] = useState<string>('');
  const [passwordVisible, setPasswordVisible] = useState(false);

  const handleLogin = async () => {
    try {
      setFormError('');

      if (!formData.email || !formData.password) {
        throw new Error('Please fill in all fields');
      }

      await login(formData);

    } catch (err) {
      setFormError(err instanceof Error ? err.message : 'Login failed');
    }
  };



  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
      </View>
    );
  }

  return (
    <ScrollView
      contentContainerStyle={styles.scrollContainer}
      keyboardShouldPersistTaps="handled"
    >
      <View style={styles.container}>
        <Image source={require('../../../assets/favicon.png')} style={styles.logo} />
      <Text style={styles.title}>Adverse Drug Reaction Reporting System</Text>
      <Text style={styles.note}> </Text>
      <Text style={styles.note}><Text style={{ fontWeight: 'bold' }}>Confidentiality</Text>{'\n'}Any information related to the identities of the reporter and patient will be kept confidential.</Text>
      <Text style={styles.note}><Text style={{ fontWeight: 'bold' }}>What to Report</Text>{'\n'}Any reactions or effects which is noxious(harmful) and/or unintended, and which occurs at doses normally used for prophylaxis, diagnosis, or treatment of a disease, or for the modification of a physiological function.</Text>
      <Text style={styles.note}> </Text>

        {(formError || error) && (
          <Text style={styles.error}>{formError || error}</Text>
        )}

        <TextInput
          label="Email or Username"
          value={formData.email}
          onChangeText={(text) => setFormData({ ...formData, email: text })}
          style={styles.input}
          mode="outlined"
          autoCapitalize="none"
          activeOutlineColor={theme.colors.primary}
          left={<TextInput.Icon icon="account" color={theme.colors.primary} />}
        />

        <TextInput
          label="Password"
          value={formData.password}
          onChangeText={(text) => setFormData({ ...formData, password: text })}
          style={styles.input}
          mode="outlined"
          secureTextEntry={!passwordVisible}
          activeOutlineColor={theme.colors.primary}
          left={<TextInput.Icon icon="lock" color={theme.colors.primary} />}
          right={
            <TextInput.Icon
              icon={passwordVisible ? "eye" : "eye-off"}
              color={theme.colors.primary}
              onPress={() => setPasswordVisible(!passwordVisible)}
            />
          }
        />

        <Button
          mode="contained"
          onPress={handleLogin}
          style={styles.button}
          buttonColor={theme.colors.primary}
        >
          Login
        </Button>

        <View style={styles.passwordResetContainer}>
          <Button
            mode="text"
            onPress={() => navigation.navigate('ForgotPassword')}
            style={styles.linkButton}
            textColor={theme.colors.primary}
          >
            Request Reset Token
          </Button>

          <Button
            mode="text"
            onPress={() => navigation.navigate('ResetPassword')}
            style={styles.linkButton}
            textColor={theme.colors.primary}
          >
            I Have a Reset Token
          </Button>
        </View>

        <Button
          mode="text"
          onPress={() => navigation.navigate('Register')}
          style={styles.linkButton}
          textColor={theme.colors.primary}
        >
          New patient? Create account
        </Button>



      <Text style={styles.note}> </Text>
      <Text style={styles.note}>Pharmacovigilance Unit encourages the reporting of all suspected adverse reactions to drugs and other medicinal substances(including herbal, traditional or alternative remedies)</Text>
      <Text style={styles.note}>Please report even you are not certain the product caused the adverse reaction</Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  scrollContainer: {
    flexGrow: 1,
  },
  container: {
    ...globalStyles.container,
    justifyContent: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.background,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    color: theme.colors.primary,
    marginBottom: theme.spacing.lg,
  },
  input: {
    ...globalStyles.input,
  },
  button: {
    ...globalStyles.button,
  },
  linkButton: {
    marginTop: theme.spacing.sm,
  },
  passwordResetContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: theme.spacing.sm,
    paddingHorizontal: theme.spacing.md,
  },
  error: {
    ...globalStyles.error,
    textAlign: 'center',
    marginBottom: theme.spacing.md,
  },
  note: {
    fontSize: 16,
    color: theme.colors.placeholder,
    textAlign: 'center',
    marginTop: theme.spacing.md,
    fontStyle: 'italic',
  },
  note_text: {
    fontSize: 16,
    color: theme.colors.placeholder,
    marginTop: theme.spacing.md,
    fontStyle: 'italic',
  },
  logo: {
    width: 300,
    height: 200,
    alignSelf: 'center',
    marginBottom: theme.spacing.lg,
  },
});
