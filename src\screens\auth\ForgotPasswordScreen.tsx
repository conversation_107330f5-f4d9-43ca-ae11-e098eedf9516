import React, { useState } from 'react';
import { View, StyleSheet, ScrollView, Image } from 'react-native';
import { TextInput, Button, Text, ActivityIndicator } from 'react-native-paper';
import { theme, styles as globalStyles } from '../../theme';
import { useAuth } from '../../context/AuthContext';
import type { ForgotPasswordData } from '../../types';

export const ForgotPasswordScreen = ({ navigation }: any) => {
  const { forgotPassword, isLoading, error } = useAuth();
  const [formData, setFormData] = useState<ForgotPasswordData>({
    email: '',
  });
  const [formError, setFormError] = useState<string>('');
  const [successMessage, setSuccessMessage] = useState<string>('');

  const handleForgotPassword = async () => {
    try {
      setFormError('');
      setSuccessMessage('');

      // Validation
      if (!formData.email || !formData.email.trim()) {
        throw new Error('Please enter your email address');
      }

      // Email format validation
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(formData.email.trim())) {
        throw new Error('Please enter a valid email address');
      }

      const message = await forgotPassword({ email: formData.email.trim().toLowerCase() });
      setSuccessMessage(message);
      
      // Clear form
      setFormData({ email: '' });

    } catch (err) {
      setFormError(err instanceof Error ? err.message : 'Failed to send reset email');
    }
  };

  const navigateToLogin = () => {
    navigation.navigate('Login');
  };

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
        <Text style={styles.loadingText}>Sending reset email...</Text>
      </View>
    );
  }

  return (
    <ScrollView
      contentContainerStyle={styles.scrollContainer}
      keyboardShouldPersistTaps="handled"
    >
      <View style={styles.container}>
        <Image source={require('../../../assets/favicon.png')} style={styles.logo} />
        
        <View style={styles.header}>
          <Text style={styles.title}>Forgot Password</Text>
          <Text style={styles.subtitle}>
            Enter your email address and we'll send you a link to reset your password
          </Text>
        </View>

        {(formError || error) && (
          <View style={styles.errorContainer}>
            <Text style={styles.error}>{formError || error}</Text>
          </View>
        )}

        {successMessage && (
          <View style={styles.successContainer}>
            <Text style={styles.success}>{successMessage}</Text>
            <Text style={styles.successNote}>
              Please check your email for a reset token. Copy the token from your email and use the button below to enter it.
              The token will expire in 15 minutes.
            </Text>
          </View>
        )}

        <TextInput
          label="Email Address"
          value={formData.email}
          onChangeText={(text) => setFormData({ email: text })}
          style={styles.input}
          mode="outlined"
          autoCapitalize="none"
          keyboardType="email-address"
          activeOutlineColor={theme.colors.primary}
          left={<TextInput.Icon icon="email" color={theme.colors.primary} />}
          disabled={!!successMessage}
        />

        <Button
          mode="contained"
          onPress={handleForgotPassword}
          style={styles.button}
          buttonColor={theme.colors.primary}
          disabled={!!successMessage}
        >
          Send Reset Token
        </Button>

        <Button
          mode="text"
          onPress={navigateToLogin}
          style={styles.linkButton}
          textColor={theme.colors.primary}
        >
          Back to Login
        </Button>

        {successMessage && (
          <>
            <Button
              mode="contained"
              onPress={() => navigation.navigate('ResetPassword')}
              style={styles.button}
              buttonColor={theme.colors.primary}
            >
              Enter Reset Token
            </Button>

            <Button
              mode="outlined"
              onPress={() => {
                setSuccessMessage('');
                setFormData({ email: '' });
              }}
              style={styles.linkButton}
              textColor={theme.colors.primary}
            >
              Send Another Reset Email
            </Button>
          </>
        )}
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  scrollContainer: {
    flexGrow: 1,
    justifyContent: 'center',
  },
  container: {
    ...globalStyles.container,
    justifyContent: 'center',
    paddingHorizontal: theme.spacing.lg,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.background,
  },
  loadingText: {
    marginTop: theme.spacing.md,
    fontSize: 16,
    color: theme.colors.text,
  },
  logo: {
    width: 100,
    height: 100,
    alignSelf: 'center',
    marginBottom: theme.spacing.lg,
  },
  header: {
    alignItems: 'center',
    marginBottom: theme.spacing.xl,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: theme.colors.primary,
    marginBottom: theme.spacing.sm,
  },
  subtitle: {
    fontSize: 16,
    color: theme.colors.text,
    textAlign: 'center',
    lineHeight: 22,
    paddingHorizontal: theme.spacing.md,
  },
  input: {
    ...globalStyles.input,
    backgroundColor: theme.colors.surface,
    marginBottom: theme.spacing.md,
  },
  button: {
    ...globalStyles.button,
    marginVertical: theme.spacing.md,
  },
  linkButton: {
    marginVertical: theme.spacing.sm,
  },
  errorContainer: {
    backgroundColor: '#ffebee',
    padding: theme.spacing.md,
    borderRadius: 8,
    marginBottom: theme.spacing.md,
    borderLeftWidth: 4,
    borderLeftColor: '#f44336',
  },
  error: {
    color: '#d32f2f',
    fontSize: 14,
    textAlign: 'center',
  },
  successContainer: {
    backgroundColor: '#e8f5e8',
    padding: theme.spacing.md,
    borderRadius: 8,
    marginBottom: theme.spacing.md,
    borderLeftWidth: 4,
    borderLeftColor: '#4caf50',
  },
  success: {
    color: '#2e7d32',
    fontSize: 14,
    fontWeight: '600',
    textAlign: 'center',
    marginBottom: theme.spacing.sm,
  },
  successNote: {
    color: '#388e3c',
    fontSize: 12,
    textAlign: 'center',
    lineHeight: 16,
  },
});
